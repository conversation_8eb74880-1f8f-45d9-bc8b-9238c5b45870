/* pages/bazi/bazi.wxss - 子平八字页面样式 */

.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.3);
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255,255,255,0.8);
  font-style: italic;
}

/* 输入区域 */
.input-section {
  background: rgba(255,255,255,0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.input-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.question-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  background: #fafafa;
}

/* 出生信息 */
.birth-info {
  margin-top: 30rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 15rpx 0;
}

.info-row .label {
  width: 160rpx;
  margin-bottom: 0;
  margin-right: 20rpx;
}

.picker {
  flex: 1;
  padding: 15rpx 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.radio {
  margin-right: 30rpx;
  font-size: 28rpx;
}

/* 开始按钮 */
.start-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 30rpx;
}

.start-btn[disabled] {
  background: #ccc;
}

/* 结果显示区域 */
.result-section {
  background: rgba(255,255,255,0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 3rpx solid #667eea;
}

/* 四柱八字 */
.bazi-chart {
  margin-bottom: 40rpx;
}

.pillars {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.pillar {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12rpx;
  margin: 0 5rpx;
}

.pillar-name {
  display: block;
  font-size: 24rpx;
  color: #fff;
  margin-bottom: 8rpx;
}

.pillar-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

/* 竖向排列的四柱显示 */
.pillar-value-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8rpx;
}

.pillar-stem {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1.2;
}

.pillar-branch {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1.2;
  margin-top: 4rpx;
}

.daymaster {
  text-align: center;
  margin-top: 20rpx;
  padding: 15rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #667eea;
  font-weight: bold;
}

/* 格局分析 */
.pattern-analysis {
  margin-bottom: 40rpx;
}

.analysis-content {
  background: #f8f9ff;
  padding: 25rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #667eea;
}

.pattern, .strength {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.pattern-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 个人信息 */
.personal-info {
  margin-bottom: 40rpx;
}

.info-sections {
  background: #f5f7fa;
  padding: 25rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #50c878;
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.info-section {
  background: rgba(255,255,255,0.8);
  padding: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.info-section-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #e9ecef;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 15rpx;
  background: #f8f9fa;
  border-radius: 6rpx;
  border: 1rpx solid #e9ecef;
}

.info-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  min-width: 70rpx;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 15rpx;
}

.amulet-recommendation {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border-radius: 8rpx;
  border: 2rpx solid #f59e0b;
}

.amulet-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.amulet-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  font-style: italic;
}

/* 五行力量分布 */
.wuxing-distribution {
  margin-bottom: 40rpx;
}

.wuxing-chart-container {
  background: #f8f9ff;
  padding: 30rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #8b5cf6;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-circle {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
  background: linear-gradient(45deg, #22c55e 0%, #ef4444 25%, #f59e0b 50%, #6b7280 75%, #3b82f6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-center {
  width: 180rpx;
  height: 180rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.center-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.center-subtitle {
  font-size: 24rpx;
  color: #666;
}

.wuxing-legend {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: rgba(255,255,255,0.8);
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.legend-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.legend-percentage {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.wuxing-analysis {
  margin-top: 25rpx;
  padding: 20rpx;
  background: rgba(255,255,255,0.6);
  border-radius: 8rpx;
  width: 100%;
}

.analysis-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.analysis-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.analysis-suggestions {
  margin-top: 15rpx;
}

.suggestion-title {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.suggestion-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 5rpx;
}

/* 神煞信息 */
.shensha-info {
  margin-bottom: 40rpx;
}

.shensha-content {
  background: #fff8f0;
  padding: 25rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff8c00;
}

.shensha-list {
  margin-bottom: 20rpx;
}

.shensha-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background: rgba(255,255,255,0.8);
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.shensha-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  min-width: 120rpx;
}

.shensha-position {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin: 0 15rpx;
}

.shensha-desc {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

.shensha-analysis {
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(255,255,255,0.6);
  border-radius: 8rpx;
}

.analysis-summary {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
  text-align: center;
}

.shensha-categories {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.category-section {
  padding: 15rpx;
  border-radius: 8rpx;
  background: rgba(255,255,255,0.5);
}

.category-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.category-title.lucky {
  color: #22c55e;
}

.category-title.unlucky {
  color: #ef4444;
}

.category-title.neutral {
  color: #6b7280;
}

.category-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.category-item {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

/* 综合分析 */
.comprehensive-analysis {
  margin-bottom: 40rpx;
}

.analysis-content {
  background: #f0f8ff;
  padding: 25rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #4169e1;
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.subsection-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #e6f3ff;
}

/* 评分部分 */
.scores-section {
  background: rgba(255,255,255,0.8);
  padding: 20rpx;
  border-radius: 10rpx;
}

.scores-grid {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.score-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 80rpx;
}

.score-bar {
  flex: 1;
  height: 20rpx;
  background: #e9ecef;
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.score-value {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
  min-width: 60rpx;
  text-align: right;
}

.overall-score {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #4169e1 0%, #6495ed 100%);
  border-radius: 10rpx;
  color: white;
}

.overall-label {
  display: block;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  opacity: 0.9;
}

.overall-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.overall-rank {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
}

/* 百分比部分 */
.percentages-section {
  background: rgba(255,255,255,0.8);
  padding: 20rpx;
  border-radius: 10rpx;
}

.percentage-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.percentage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #4169e1;
}

.percentage-label {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.percentage-value {
  font-size: 26rpx;
  color: #4169e1;
  font-weight: bold;
}

/* 建议部分 */
.recommendations-section {
  background: rgba(255,255,255,0.8);
  padding: 20rpx;
  border-radius: 10rpx;
}

.recommendations-list {
  margin-bottom: 20rpx;
}

.recommendation-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
  padding-left: 10rpx;
}

.summary-box {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  border-radius: 8rpx;
  border: 2rpx solid #f59e0b;
}

.summary-text {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.5;
}

/* 十神分布 */
.tengod-analysis {
  margin-bottom: 40rpx;
}

.tengod-content {
  background: #f0f8ff;
  padding: 25rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #4facfe;
}

.tengod-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.8;
  white-space: pre-line;
}

/* 大运流年 */
.luck-analysis {
  margin-bottom: 40rpx;
}

.luck-content {
  background: #fff5f5;
  padding: 25rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff6b6b;
}

.current-luck, .current-year {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

/* 专项分析 */
.custom-analysis {
  margin-bottom: 40rpx;
}

.question-type {
  display: block;
  font-size: 28rpx;
  color: #667eea;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.analysis-result {
  display: block;
  font-size: 26rpx;
  color: #333;
  line-height: 1.8;
  white-space: pre-line;
}

/* 真太阳时信息样式 */
.solar-time-info {
  background: linear-gradient(135deg, #f8f8f0, #f0f0e8);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 16rpx;
  border-left: 4rpx solid var(--ancient-gold);
}

.solar-time-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.solar-time-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
  white-space: pre-line;
}

.solar-time-result {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #667eea;
}

/* 重新排盘按钮 */
.restart-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-top: 20rpx;
}

/* 预分析对话界面样式 */
.conversation-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.conversation-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 30rpx;
  color: white;
  position: relative;
}

.conversation-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.conversation-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

.close-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
}

.conversation-content {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

.conversation-history {
  flex: 1;
  padding: 30rpx;
  max-height: calc(100vh - 300rpx);
}

.message-item {
  margin-bottom: 30rpx;
}

.message {
  max-width: 80%;
  padding: 20rpx 25rpx;
  border-radius: 20rpx;
  position: relative;
}

.ai-message {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-right: auto;
  border-bottom-left-radius: 8rpx;
}

.user-message {
  background: #f0f0f0;
  color: #333;
  margin-left: auto;
  border-bottom-right-radius: 8rpx;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-time {
  font-size: 22rpx;
  opacity: 0.7;
  margin-top: 10rpx;
}

.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.typing-dots {
  display: flex;
  margin-right: 15rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: #667eea;
  border-radius: 50%;
  margin-right: 8rpx;
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 26rpx;
  color: #666;
}

.conversation-input-area {
  border-top: 1rpx solid #eee;
  padding: 20rpx 30rpx;
  background: white;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}

.conversation-input {
  flex: 1;
  min-height: 80rpx;
  max-height: 200rpx;
  padding: 15rpx 20rpx;
  border: 2rpx solid #eee;
  border-radius: 25rpx;
  font-size: 28rpx;
  background: #f9f9f9;
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}