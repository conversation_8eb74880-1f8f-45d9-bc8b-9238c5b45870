<!--pages/bazi/bazi.wxml - 子平八字页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">子平八字</text>
    <text class="subtitle">朱熹原著·传统命理</text>
  </view>

  <!-- 输入区域 -->
  <view class="input-section" wx:if="{{!showResult}}">
    <!-- 问题输入 -->
    <view class="input-group">
      <text class="label">请输入您的问题：</text>
      <textarea
        class="question-input"
        placeholder="例如：我的财运如何？何时能升职？婚姻运势怎样？"
        value="{{question}}"
        bindinput="onQuestionInput"
        maxlength="200"
      ></textarea>
    </view>

    <!-- 出生信息 -->
    <view class="birth-info">
      <view class="info-row">
        <text class="label">出生日期：</text>
        <picker mode="date" value="{{birthDate}}" bindchange="onDateChange">
          <view class="picker">{{birthDate || '请选择'}}</view>
        </picker>
      </view>

      <view class="info-row">
        <text class="label">出生时间：</text>
        <picker mode="time" value="{{birthTime}}" bindchange="onTimeChange">
          <view class="picker">{{birthTime || '请选择'}}</view>
        </picker>
      </view>

      <view class="info-row">
        <text class="label">出生地：</text>
        <picker range="{{cityList}}" range-key="name" value="{{selectedCityIndex}}" bindchange="onCityChange">
          <view class="picker">{{selectedCity || '请选择出生地'}}</view>
        </picker>
      </view>

      <view class="info-row" wx:if="{{useTrueSolarTime}}">
        <view class="solar-time-info">
          <text class="solar-time-title">真太阳时修正</text>
          <text class="solar-time-desc">{{solarTimeExplanation}}</text>
          <text class="solar-time-result">修正后时间：{{trueSolarTimeString}}</text>
        </view>
      </view>

      <view class="info-row">
        <text class="label">性别：</text>
        <radio-group bindchange="onGenderChange">
          <label class="radio">
            <radio value="男" checked="{{isMale}}" />男
          </label>
          <label class="radio">
            <radio value="女" checked="{{!isMale}}" />女
          </label>
        </radio-group>
      </view>
    </view>

    <!-- 开始按钮 -->
    <button
      class="start-btn"
      bindtap="onStartAnalysis"
      loading="{{isAnalyzing}}"
      disabled="{{isAnalyzing}}"
    >
      {{isAnalyzing ? '正在排盘...' : '开始排盘'}}
    </button>
  </view>

  <!-- 结果显示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <!-- 四柱八字 -->
    <view class="bazi-chart">
      <text class="section-title">四柱八字</text>
      <view class="pillars">
        <view class="pillar">
          <text class="pillar-name">年柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.yearStem}}</text>
            <text class="pillar-branch">{{formattedBazi.yearBranch}}</text>
          </view>
        </view>
        <view class="pillar">
          <text class="pillar-name">月柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.monthStem}}</text>
            <text class="pillar-branch">{{formattedBazi.monthBranch}}</text>
          </view>
        </view>
        <view class="pillar">
          <text class="pillar-name">日柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.dayStem}}</text>
            <text class="pillar-branch">{{formattedBazi.dayBranch}}</text>
          </view>
        </view>
        <view class="pillar">
          <text class="pillar-name">时柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.hourStem}}</text>
            <text class="pillar-branch">{{formattedBazi.hourBranch}}</text>
          </view>
        </view>
      </view>
      <view class="daymaster">
        <text>日主：{{analysis.dayMaster}}（{{analysis.dayMasterElement}}）</text>
      </view>
    </view>

    <!-- 格局分析 -->
    <view class="pattern-analysis">
      <text class="section-title">格局分析</text>
      <view class="analysis-content">
        <text class="pattern">格局：{{analysis.pattern.pattern}}</text>
        <text class="strength">强弱：{{analysis.strength.level}}</text>
        <text class="pattern-desc">{{analysis.pattern.analysis[0] || '普通格局，需要综合分析'}}</text>
      </view>
    </view>

    <!-- 个人信息 -->
    <view class="personal-info" wx:if="{{traditionalInfo}}">
      <text class="section-title">个人信息</text>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">性别</text>
          <text class="info-value">{{traditionalInfo.personalInfo.gender}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">年龄</text>
          <text class="info-value">{{traditionalInfo.personalInfo.age}}岁</text>
        </view>
        <view class="info-item">
          <text class="info-label">出生地</text>
          <text class="info-value">{{traditionalInfo.personalInfo.birthLocation}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">阳历</text>
          <text class="info-value">{{traditionalInfo.personalInfo.solarCalendar}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">纳音</text>
          <text class="info-value">{{traditionalInfo.nayin}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">五行命</text>
          <text class="info-value">{{traditionalInfo.wuxingDestiny}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">生肖</text>
          <text class="info-value">{{traditionalInfo.zodiac}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">星座</text>
          <text class="info-value">{{traditionalInfo.constellation}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">本命佛</text>
          <text class="info-value">{{traditionalInfo.benmingBuddha}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">命卦</text>
          <text class="info-value">{{traditionalInfo.mingGua}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">吉祥物</text>
          <text class="info-value">{{traditionalInfo.zodiacAmulet}}</text>
        </view>
      </view>
    </view>

    <!-- 十神分布 -->
    <view class="tengod-analysis" wx:if="{{showResult}}">
      <text class="section-title">十神分布</text>
      <view class="tengod-content">
        <text class="tengod-text">{{analysis && analysis.tenGods ? formatTenGodsDistribution(analysis.tenGods.distribution) : '十神分析中...'}}</text>
      </view>
    </view>

    <!-- 大运流年 -->
    <view class="luck-analysis" wx:if="{{showResult}}">
      <text class="section-title">大运流年</text>
      <view class="luck-content">
        <text class="current-luck">当前大运：{{formatDayunInfo(currentDayun)}}</text>
        <text class="current-year">当前流年：{{currentLiunian ? currentLiunian.ganzhi + '年' : '计算中...'}}</text>
      </view>
    </view>

    <!-- 专项分析 -->
    <view class="custom-analysis">
      <text class="section-title">专项分析</text>
      <view class="analysis-content">
        <text class="question-type">问题类型：{{customAnalysis.questionType}}</text>
        <text class="analysis-result">{{formatBaziAnalysis(customAnalysis)}}</text>
      </view>
    </view>

    <!-- AI智能分析 -->
    <view class="ai-analysis" wx:if="{{analysis.aiAnalysis}}">
      <text class="section-title">AI智能解读</text>
      <view class="analysis-content">
        <text class="ai-result">{{analysis.aiAnalysis}}</text>
      </view>
    </view>

    <!-- 重新排盘按钮 -->
    <button class="restart-btn" bindtap="onRestart">重新排盘</button>
  </view>

  <!-- 预分析对话界面 -->
  <view class="conversation-panel" wx:if="{{showConversationPanel}}">
    <view class="conversation-header">
      <text class="conversation-title">精准度提升咨询</text>
      <text class="conversation-subtitle">为了提供更准确的分析，请回答以下问题</text>
      <view class="close-btn" bindtap="closeConversationMode">×</view>
    </view>

    <view class="conversation-content">
      <!-- 对话历史 -->
      <scroll-view class="conversation-history" scroll-y="true" scroll-top="{{scrollTop}}">
        <view class="message-item" wx:for="{{conversationHistory}}" wx:key="timestamp">
          <view class="message {{item.type === 'ai' ? 'ai-message' : 'user-message'}}">
            <view class="message-content">{{item.content}}</view>
            <view class="message-time">{{item.timestamp}}</view>
          </view>
        </view>

        <!-- AI正在打字指示器 -->
        <view class="typing-indicator" wx:if="{{isTyping}}">
          <view class="typing-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <text class="typing-text">AI正在思考...</text>
        </view>
      </scroll-view>

      <!-- 输入区域 -->
      <view class="conversation-input-area" wx:if="{{isWaitingResponse}}">
        <view class="input-container">
          <textarea
            class="conversation-input"
            placeholder="请输入您的回答..."
            value="{{conversationInput}}"
            bindinput="onConversationInput"
            maxlength="500"
          ></textarea>
          <button class="send-btn" bindtap="handleUserResponse">发送</button>
        </view>
      </view>
    </view>
  </view>
</view>